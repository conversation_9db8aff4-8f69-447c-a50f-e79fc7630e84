# import sys
# from PyQt6.QtCore import QUrl, Qt, QSettings
# from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit
# from PyQt6.QtWebEngineWidgets import QWebEngineView
# from PyQt6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile
# import os
# class WebRTCH264Player(QMainWindow):
#     def __init__(self):
#         super().__init__()
        
#         # 配置WebRTC H.264
#         self.configure_webrtc()
        
#         self.setWindowTitle("UOS ARM64 WebRTC H.264播放器")
#         self.resize(1024, 768)
        
#         central_widget = QWidget()
#         self.setCentralWidget(central_widget)
#         layout = QVBoxLayout(central_widget)
        
#         self.web_view = QWebEngineView()
        
#         # 启用必要的WebEngine设置
#         settings = self.web_view.settings()
#         settings.setAttribute(QWebEngineSettings.WebAttribute.PlaybackRequiresUserGesture, False)
#         settings.setAttribute(QWebEngineSettings.WebAttribute.WebRTCPublicInterfacesOnly, False)
#         settings.setAttribute(QWebEngineSettings.WebAttribute.AllowRunningInsecureContent, True)
        
#         # 启用硬件加速解码（如果可用）
#         profile = QWebEngineProfile.defaultProfile()
#         profile.setHttpCacheType(QWebEngineProfile.HttpCacheType.DiskHttpCache)
        
#         layout.addWidget(self.web_view)
        
#         # 加载WebRTC测试页面
#         self.web_view.setUrl(QUrl("http://192.168.0.2:8889/stream05"))
        
#         # 显示当前支持的编解码器
#         self.web_view.page().toPlainText(lambda text: print(f"Page content: {text[:200]}..."))
    
#     def configure_webrtc(self):
#         # 通过环境变量和QSettings配置
#         QSettings.setDefaultFormat(QSettings.Format.IniFormat)
#         settings = QSettings("QtProject", "QtWebEngine")
#         settings.setValue("WebRTC/H264", True)
#         settings.setValue("WebRTC/EnableHWDecoding", True)
#         settings.sync()
        
#         # 设置Chromium标志
#         os.environ["QTWEBENGINE_CHROMIUM_FLAGS"] = (
#             "--enable-features=WebRTC-H264WithOpenH264FFmpeg "
#             "--disable-features=WebRTC-H264WithOpenH264FFmpeg/EnabledByDefault "
#             "--enable-webrtc-h264-with-openh264-ffmpeg"
#         )

# if __name__ == "__main__":
#     # ARM平台可能需要这些设置
#     os.environ["QT_QUICK_BACKEND"] = "software"
#     os.environ["QT_WEBENGINE_DISABLE_SANDBOX"] = "1"
    
#     app = QApplication(sys.argv)
#     player = WebRTCH264Player()
#     player.show()
#     sys.exit(app.exec())


import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtCore import QUrl

class WebRTCPlayer(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 设置窗口
        self.setWindowTitle("PyQt6 WebRTC播放器")
        self.resize(800, 600)
        
        # 创建浏览器视图
        self.browser = QWebEngineView()
        
        # 启用WebRTC设置
        self.browser.settings().setAttribute(
            self.browser.settings().WebAttribute.WebRTCPublicInterfacesOnly, False)
        
        # 加载WebRTC测试页面
        self.browser.setUrl(QUrl("http://192.168.0.2:8889/stream01"))
        
        # 设置布局
        central_widget = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(self.browser)
        central_widget.setLayout(layout)
        self.setCentralWidget(central_widget)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    player = WebRTCPlayer()
    player.show()
    sys.exit(app.exec())