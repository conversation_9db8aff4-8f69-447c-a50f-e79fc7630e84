import sys
import json
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QMessageBox
from PyQt5.QtCore import QFile, QTextStream,Qt
from wakeonlan import send_magic_packet
import subprocess


class ControlPC:
    def __init__(self):
        self.computers = self.load_computers_from_json(
            "database/computers.json")  # 从 JSON 文件加载数据

    def load_computers_from_json(self, file_path):
        """从 JSON 文件加载计算机数据"""
        try:
            with open(file_path, "r", encoding="utf-8") as file:
                data = json.load(file)
                return [(item["ip"], item["mac"]) for item in data]
        except Exception as e:
            print(f"Error loading JSON file: {e}")
            return []

    def shutdown(self):
        print("Shutting down computers...")
        for ip, _ in self.computers:
            print(f"Attempting to shut down: {ip}")
            try:
                result = subprocess.run(
                    ['shutdown', '/s', '/m', f'\\\\{ip}', '/t', '0'],
                    check=True,
                    capture_output=True,
                    text=True
                )
                print(f"Shutdown command sent to: {ip}")
            except subprocess.CalledProcessError as e:
                print(
                    f"Failed to shut down: {ip}. Error: {e}. Output: {e.output if e.output else 'No additional output'}")

    def wake_on_lan(self):
        print("Waking up computers...")
        for _, mac in self.computers:
            send_magic_packet(mac)
            print(f"Magic packet sent to: {mac}")


class ControlPCAPP(QMainWindow):
    def __init__(self):
        super().__init__()
        self.control_pc = ControlPC()  # 初始化 ControlPC
        self.initUI()

    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle("PC 远程控制")
        self.setGeometry(600, 400, 400, 200)
        self.setWindowFlags(Qt.Tool | self.windowFlags())

        # 主布局
        central_widget = QWidget(self)
        layout = QVBoxLayout(central_widget)

        # 标题
        title_label = QLabel("远程控制计算机", self)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold;")
        layout.addWidget(title_label)

        # 唤醒按钮
        wake_button = QPushButton("唤醒计算机", self)
        wake_button.clicked.connect(self.wake_on_lan)
        layout.addWidget(wake_button)

        # 关机按钮
        shutdown_button = QPushButton("关闭计算机", self)
        shutdown_button.clicked.connect(self.shutdown)
        layout.addWidget(shutdown_button)

        # 设置主布局
        self.setCentralWidget(central_widget)

    def wake_on_lan(self):
        """唤醒计算机"""
        self.control_pc.wake_on_lan()
        QMessageBox.information(self, "成功", "唤醒命令已发送！")

    def shutdown(self):
        """关闭计算机"""
        self.control_pc.shutdown()
        QMessageBox.information(self, "成功", "关机命令已发送！")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ControlPCAPP()
    window.show()
    sys.exit(app.exec_())
