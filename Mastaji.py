import sys
import pickle
import hashlib
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from SysCss import MyCss
from Screen_Broadcasting import BroadcastApp
from Screen_DLAN import ScreenDLAN
from File_sharing import FileSharingApp
from White_board import Whiteboard
from Select_People import RandomStudentPicker
from Open_Platform import OpenPlatformApp
from Group_screen import Groupscreen
from Danma_ku import DanmakuTeacherApp
from Control_PC import ControlPCAPP
# from AI_Attendance import AttendanceSystem
from registration_app import *
from login_manager import get_login_manager

class MainWindow(QMainWindow):
    def __init__(self, teacher_info=None, current_course_id=None):
        super().__init__()

        # 获取登录管理器
        self.login_manager = get_login_manager()

        # 教师登录信息 - 优先使用传入的信息，否则从登录管理器获取
        if teacher_info:
            self.teacher_info = teacher_info
        else:
            self.teacher_info = self.login_manager.get_teacher_info()

        # 当前课程ID
        self.current_course_id = current_course_id

        # 验证登录状态
        if not self.teacher_info:
            self.require_login_and_restart()
            return

        # 初始化应用实例为 None，延迟加载
        self.Attendance_S = None
        self.white_B = None
        self.screen_B = None
        self.screen_D = None
        self.File_S = None
        self.Select_P = None
        self.Open_P = None
        self.Group_s = None
        self.Danma_k = None
        self.Control_P = None
        

        # 注册状态和试用期
        self.registered = False
        self.trial_period = False
        self.trial_remaining_seconds = 0  # 试用期剩余时间（秒）
        self.hardware_fingerprint = None
        self.load_registration_state()  # 加载注册状态
        # 检查试用期或注册状态
        self.check_trial_or_registration()

        # 初始化倒计时
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_timer)
        self.remaining_time = 3600  # 倒计时时间（秒）
        # 启动倒计时
        self.timer.start(1000)  # 每秒更新一次

        # 获取屏幕尺寸（高DPI适配）
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()
        
        # 统信专用穿透配置
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
      
        # 主窗口初始位置（右侧外部，宽度0避免影响）
        self.setGeometry(self.screen_width, (self.screen_height - 600) // 2, 0, 600)

        # 导航菜单层（独立窗口）
        self.Navigation_Menu = QWidget()
        self.Navigation_Menu.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.Navigation_Menu.setGeometry(self.screen_width, (self.screen_height - 600) // 2, 200, 600)
        self.Navigation_Menu.setStyleSheet(MyCss.mainBgcolora)
        self.Navigation_Menu.show()
        

        # 使用 QVBoxLayout 布局（垂直布局）
        self.vertical_layout = QVBoxLayout(self.Navigation_Menu)
        self.vertical_layout.setSpacing(2)
        self.vertical_layout.setContentsMargins(10, 5, 10, 5)

        # 第一行：倒计时显示
        self.timer_widget = QWidget(self.Navigation_Menu)
        self.timer_widget.setFixedHeight(100)  # 设置高度为 100
        self.timer_layout = QVBoxLayout(self.timer_widget)
        self.timer_layout.setSpacing(0)
        self.timer_layout.setContentsMargins(0, 0, 0, 0)

        self.timer_label = QLabel("00:00", self.timer_widget)
        self.timer_label.setAlignment(Qt.AlignCenter)  # 居中对齐
        self.timer_label.setStyleSheet("font-size: 18px; font-weight: bold; color: white;")  # 设置样式
        self.timer_layout.addWidget(self.timer_label)
        self.vertical_layout.addWidget(self.timer_widget)

        # 第二行：按钮布局
        self.button_widget = QWidget(self.Navigation_Menu)
        self.button_layout = QGridLayout(self.button_widget)
        self.button_layout.setSpacing(2)
        self.button_layout.setContentsMargins(0, 0, 0, 0)

        # 按钮状态字典，记录每个按钮的选中状态
        self.button_states = {}
        buttons = [
            # ("签到", self.show_attendance, "AI人脸无感识别签到"),
            ("批注", self.show_whiteboard, "打开电子批注"),
            ("投屏", self.show_screen_dlan, "将屏幕投放到其他设备"),
            ("广播", self.show_screen_broadcast, "广播屏幕内容"),
            ("互动", self.show_group_screen, "与分组屏幕互动"),
            ("弹幕", self.show_danmaku, "分组端发送弹幕"),
            ("选人", self.show_random_picker, "随机选人"),
            
            ("控制", self.show_control_pc, "一键开关分组屏"),
            ("分享", self.show_file_sharing, "扫码下载"),
            ("平台", self.show_open_platform, "可视化资源平台"),
            ("注册", self.show_registration, "注册系统"),
            ("退出", self.CloseBotton, "退出系统"),
        ]

        # 将按钮添加到网格布局中
        for i, (text, callback, tooltip) in enumerate(buttons):
            row = i // 2  # 行号
            col = i % 2   # 列号
            button = QPushButton(text, self.button_widget)
            button.setFixedSize(64, 50)  # 设置按钮大小
            button.setStyleSheet(MyCss.butBCss)  # 按钮样式
            button.setToolTip(tooltip)  # 设置悬停提示
            button.clicked.connect(
                self.create_button_click_handler(button, text, callback))
            self.button_layout.addWidget(button, row, col)
            self.button_states[text] = False  # 初始化按钮状态为未选中

        self.vertical_layout.addWidget(self.button_widget)

        # 第三行：显示 Logo
        self.logo_widget = QWidget(self.Navigation_Menu)
        self.logo_widget.setFixedHeight(100)  # 设置高度为 100

        self.logo_layout = QVBoxLayout(self.logo_widget)
        self.logo_layout.setSpacing(0)
        self.logo_layout.setContentsMargins(0, 0, 0, 0)

        # 创建 QLabel 用于显示 Logo
        self.logo_label = QLabel(self.logo_widget)
        self.logo_label.setAlignment(Qt.AlignCenter)  # 居中显示 Logo

        # 加载 Logo 图片
        logo_pixmap = QPixmap("images/logo.png")  # 替换为你的 Logo 路径
        self.logo_label.setPixmap(logo_pixmap.scaled(
            100, 100, Qt.KeepAspectRatio))  # 缩放 Logo 到合适大小

        # 将 Logo 添加到布局
        self.logo_layout.addWidget(self.logo_label)

        # 将 Logo 组件添加到垂直布局
        self.vertical_layout.addWidget(self.logo_widget)
        
        # 独立箭头按钮（优化版）
        self.arrow_button = QPushButton("→")
        self.arrow_button.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint |Qt.X11BypassWindowManagerHint)
        self.arrow_button.setAttribute(Qt.WA_TranslucentBackground)
        self.arrow_button.setAttribute(Qt.WA_ShowWithoutActivating)  # Windows专用
        self.arrow_button.setGeometry(self.screen_width - 35, (self.screen_height) // 2, 50, 50)
        self.arrow_button.setStyleSheet("""
            QPushButton {
                background: rgba(100, 100, 100, 180);
                border-radius: 25px;
                color: white;
                font: bold 18px;
            }
            QPushButton:hover {
                background: rgba(130, 130, 130, 200);
            }
        """)
        self.arrow_button.clicked.connect(self.toggle_menu)
        self.arrow_button.show()

        # 动画配置
        self.animation = QPropertyAnimation(self.Navigation_Menu, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        self.animation.finished.connect(self.on_animation_finished)

        # 状态管理
        self.menu_visible = True
        self.ensure_button_top()

    def require_login_and_restart(self):
        """要求登录并重启"""
        QMessageBox.warning(self, '需要登录', '请先通过主程序登录后再使用课堂工具！')

        # 尝试显示登录窗口
        if self.login_manager.require_login(self):
            # 登录成功，更新教师信息
            self.teacher_info = self.login_manager.get_teacher_info()
            print(f"登录成功: {self.teacher_info.get('teacher_name', '未知教师')}")
        else:
            # 登录失败或取消，关闭窗口
            self.close()
    ###########################################################################################################
    def check_trial_or_registration(self):
        """检查试用期或注册状态，并在启动时提示用户"""
        if not self.registered and not self.trial_period:
            # 首次启动，初始化试用期为 1 天（86400 秒）
            self.trial_period = True
            # self.trial_remaining_seconds = 86400  # 1 天试用期（单位：秒）
            self.save_registration_state()
            QMessageBox.information(
                self, "试用期开始", f"试用期已开始，剩余时间为 {self.format_time(self.trial_remaining_seconds)}！")
        elif self.trial_period and self.trial_remaining_seconds > 0:
            # 试用期内，更新剩余时间
            QMessageBox.information(
                self, "试用中", f"试用期剩余时间为 {self.format_time(self.trial_remaining_seconds)}！")
        elif self.trial_period and self.trial_remaining_seconds <= 0:
            # 试用期已结束
            self.trial_period = False
            self.save_registration_state()
            QMessageBox.information(self, "试用期结束", "试用期已结束，请注册以继续使用！")

    def format_time(self, seconds):
        """将秒数格式化为天、小时、分钟、秒"""
        days = seconds // 86400
        hours = (seconds % 86400) // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{days}天 {hours}小时 {minutes}分钟 {seconds}秒"

    def create_button_click_handler(self, button, text, callback):
        """创建按钮点击事件处理函数"""
        def handler():
            if self.button_states[text]:  # 如果按钮已选中
                button.setStyleSheet(MyCss.butBCss)  # 恢复原状
                self.button_states[text] = False  # 更新状态为未选中
                # 关闭对应的功能模块
                if text == "批注" and self.white_B:
                    self.white_B.close()
                elif text == "无感签到" and self.Attendance_S:
                    self.Attendance_S.close()
                elif text == "投屏" and self.screen_D:
                    self.screen_D.close()
                elif text == "广播" and self.screen_B:
                    self.screen_B.close()
                elif text == "互动" and self.Group_s:
                    self.Group_s.close()
                elif text == "弹幕" and self.Danma_k:
                    self.Danma_k.close()
                elif text == "选人" and self.Select_P:
                    self.Select_P.close()
            
                elif text == "控制" and self.Control_P:
                    self.Control_P.close()
                elif text == "分享" and self.File_S:
                    self.File_S.close()
                elif text == "平台" and self.Open_P:
                    self.Open_P.close()
            else:
                # 检查是否为试用期内
                if self.trial_period and self.trial_remaining_seconds > 0:
                    # 试用期内，直接执行功能
                    button.setStyleSheet(
                        "background-color: red; color: white;")  # 设置为红色
                    self.button_states[text] = True  # 更新状态为选中
                    callback()  # 执行回调函数
                    return
                elif self.trial_period and self.trial_remaining_seconds <= 0:
                    # 试用期已结束
                    self.trial_period = False
                    self.save_registration_state()
                    QMessageBox.information(
                        self, "试用期结束", "试用期已结束，请注册以继续使用！")
                    return

                # 非试用期或试用期已结束，检查注册状态
                if text != "注册" and text != "退出" and not self.registered:
                    QMessageBox.warning(self, "未注册", "请先注册以解锁功能！")
                    return
                button.setStyleSheet(
                    "background-color: red; color: white;")  # 设置为红色
                self.button_states[text] = True  # 更新状态为选中
                callback()  # 执行回调函数
        return handler

    def load_registration_state(self):
        """从 pkl 文件加载注册状态和试用期信息"""
        try:
            with open('registration.pkl', 'rb') as f:
                data = pickle.load(f)
                self.registered = data.get('registered', False)
                self.trial_period = data.get('trial_period', False)
                self.trial_remaining_seconds = data.get(
                    'trial_remaining_seconds', 0)
                self.hardware_fingerprint = data.get(
                    'hardware_fingerprint', None)
        except (FileNotFoundError, EOFError):
            # 文件不存在或为空，初始化默认值
            self.registered = False
            self.trial_period = False
            self.trial_remaining_seconds = 0
            self.hardware_fingerprint = None

    def save_registration_state(self):
        """保存注册状态和试用期信息到 pkl 文件"""
        data = {
            'registered': self.registered,
            'trial_period': self.trial_period,
            'trial_remaining_seconds': self.trial_remaining_seconds,
            'hardware_fingerprint': self.hardware_fingerprint
        }
        with open('registration.pkl', 'wb') as f:
            pickle.dump(data, f)

    def show_registration(self):
        """显示注册模块"""
        if self.registered:
            QMessageBox.information(self, "已注册", "系统已注册，无需重复操作！")
            return

        if self.trial_period and self.trial_remaining_seconds > 0:
            QMessageBox.information(
                self, "试用中", f"试用期剩余时间为 {self.format_time(self.trial_remaining_seconds)}！")
            return

        # 生成硬件指纹
        self.hardware_fingerprint = generate_hardware_fingerprint()
        self.save_registration_state()

        # 显示硬件指纹
        QMessageBox.information(
            self, "硬件指纹", f"您的硬件指纹为：\n{self.hardware_fingerprint}")

        # 输入注册码
        code, ok = QInputDialog.getText(self, "注册", "请输入注册码：")
        if ok:
            expected_code = self.generate_registration_code(
                self.hardware_fingerprint)
            if code == expected_code:
                self.registered = True
                self.trial_period = False
                self.save_registration_state()
                QMessageBox.information(self, "注册成功", "注册成功，功能已解锁！")
            else:
                QMessageBox.warning(self, "注册失败", "注册码错误，请重试！")

    def generate_registration_code(self, hardware_fingerprint):
        """根据硬件指纹生成注册码"""
        hash_value = hashlib.md5(hardware_fingerprint.encode()).hexdigest()
        code = "-".join([hash_value[i:i+5].upper() for i in range(0, 25, 5)])
        return code
    ############### 功能模块函数 #####################################################
    def show_attendance(self):
        """显示无感签到"""
        if self.Attendance_S is None:
            self.Attendance_S = AttendanceSystem()
        self.Attendance_S.show()

    def show_whiteboard(self):
        """显示电子白板"""
        if self.white_B is None:
            self.white_B = Whiteboard()
        self.white_B.showFullScreen()

    def show_screen_dlan(self):
        """显示本机投屏"""
        if self.screen_D is None:
            self.screen_D = ScreenDLAN()
        self.screen_D.show()

    def show_screen_broadcast(self):
        """显示屏幕广播"""
        if self.screen_B is None:
            self.screen_B = BroadcastApp()
        self.screen_B.show()

    def show_group_screen(self):
        """显示多屏互动"""
        if self.Group_s is None:
            self.Group_s = Groupscreen()
        self.Group_s.show()

    def show_danmaku(self):
        """显示弹幕功能"""
        if self.Danma_k is None:
            self.Danma_k = DanmakuTeacherApp()
        self.Danma_k.showFullScreen()

    def show_random_picker(self):
        """显示随机选人"""
        if self.Select_P is None:
            self.Select_P = RandomStudentPicker(teacher_info=self.teacher_info, current_course_id=self.current_course_id)
        self.Select_P.show()


    def show_control_pc(self):
        """显示远程控制"""
        if self.Control_P is None:
            self.Control_P = ControlPCAPP()
        self.Control_P.show()

    def show_file_sharing(self):
        """显示课件分享"""
        if self.File_S is None:
            self.File_S = FileSharingApp()
        self.File_S.show()


    def show_open_platform(self):
        """显示资源云盘"""
        if self.Open_P is None:
            self.Open_P = OpenPlatformApp()
        self.Open_P.show()


    def ensure_button_top(self):
        """确保按钮始终在最顶层"""
        self.arrow_button.raise_()
        if sys.platform == "win32":
            self.arrow_button.activateWindow()
        elif sys.platform == "linux":
            self.arrow_button.setAttribute(Qt.WA_X11NetWmWindowTypeDock, True)

    def toggle_menu(self):
        """优化后的菜单切换逻辑"""
        self.menu_visible = not self.menu_visible
        
        if self.menu_visible:
            # 显示菜单
            self.Navigation_Menu.show()
            self.animation.setStartValue(QRect(
                self.screen_width,
                self.Navigation_Menu.y(),
                200,
                600
            ))
            self.animation.setEndValue(QRect(
                self.screen_width - 200,
                self.Navigation_Menu.y(),
                200,
                600
            ))
            self.arrow_button.setText("→")
        else:
            # 隐藏菜单
            self.animation.setStartValue(self.Navigation_Menu.geometry())
            self.animation.setEndValue(QRect(
                self.screen_width,
                self.Navigation_Menu.y(),
                200,
                600
            ))
            self.arrow_button.setText("←")
        
        # 确保按钮在最前
        self.ensure_button_top()
        self.animation.start()

    def on_animation_finished(self):
        """动画完成后处理"""
        if not self.menu_visible:
            self.Navigation_Menu.hide()
        self.ensure_button_top()

    def hide_menu(self):
        """自动隐藏菜单"""
        if self.menu_visible:
            self.menu_visible = False
            self.animation.setStartValue(self.Navigation_Menu.geometry())
            self.animation.setEndValue(QRect(self.screen_width,self.Navigation_Menu.y(),200,600))
            self.arrow_button.setText("→")
            self.animation.start()

    def update_timer(self):
        """更新倒计时显示"""
        if self.remaining_time > 0:
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.timer_label.setText(f"{minutes:02}:{seconds:02}")
            self.remaining_time -= 1
        else:
            self.timer.stop()
            self.timer_label.setText("时间到！")

    def CloseBotton(self):
        """退出系统提示"""
        message_box = QMessageBox(self)
        message_box.setWindowTitle("退出系统")
        message_box.setText("您确定退出系统吗？")
        message_box.setIcon(QMessageBox.Information)
        message_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
        returnValue = message_box.exec()
        if returnValue == QMessageBox.Ok:
            QApplication.instance().quit()