import sys
import threading
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QComboBox, QLabel
from PyQt5.QtCore import QThread, pyqtSignal,Qt
from flask import Flask, send_from_directory
import socket
import upnpy
import requests
from xml.sax.saxutils import escape
import subprocess
import os
import glob


class ScreenStreamer:
    """屏幕捕获和流媒体服务"""

    def __init__(self, port=8889):
        self.port = port
        self.running = False
        self.ffmpeg_process = None
        self.app = Flask(__name__)

        # 定义流媒体路由
        @self.app.route('/stream.m3u8')
        def stream_hls():
            return send_from_directory('.', 'stream.m3u8')  # 返回 HLS 播放列表文件

        @self.app.route('/<path:filename>')
        def send_file(filename):
            return send_from_directory('.', filename)  # 返回 .ts 文件

    def start_http_server(self):
        """启动HTTP服务器，提供H.264视频流"""
        self.app.run(host='0.0.0.0', port=self.port)

    def start_capture(self):
        """使用 FFmpeg 捕获屏幕内容并保存为 HLS 格式"""
        self.running = True

        # 启动 FFmpeg 进程
        command = [
            'ffmpeg',
            '-f', 'gdigrab',
            '-framerate', '60',
            '-i', 'desktop',
            '-vf', 'scale=1280:720:force_original_aspect_ratio=decrease',
            '-c:v', 'libx265',  # 使用 H.265 编码器
            '-preset', 'ultrafast',  # 更快的编码速度，减少延迟
            '-tune', 'zerolatency',
            '-x265-params', 'keyint=15:min-keyint=15:scenecut=0',  # 减少关键帧间隔
            '-g', '15',  # GOP 大小设置为 15
            '-profile:v', 'main',
            '-level', '4.0',
            '-pix_fmt', 'yuv420p',
            '-b:v', '1M',
            '-f', 'hls',
            '-hls_time', '0.0',  # 每个片段时长（秒）
            '-hls_list_size', '2',  # 播放列表中的片段数量
            '-hls_flags', 'delete_segments',  # 删除旧的片段
            'stream.m3u8'  # 输出 HLS 播放列表
        ]
        try:
            self.ffmpeg_process = subprocess.Popen(
                command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            print("屏幕流媒体编码器已启动")
            # 启动一个线程捕获 FFmpeg 的输出
            threading.Thread(target=self.handle_ffmpeg_output, args=(
                self.ffmpeg_process,), daemon=True).start()
        except Exception as e:
            print(f"启动FFmpeg失败: {str(e)}")
            self.stop()

    def handle_ffmpeg_output(self, process):
        for line in process.stderr:
            print(f"FFmpeg: {line.decode().strip()}")

    def start(self):
        """启动流媒体服务和屏幕捕获"""
        threading.Thread(target=self.start_http_server, daemon=True).start()
        self.start_capture()

    def stop(self):
        """停止服务并删除生成的流文件"""
        self.running = False
        if self.ffmpeg_process:
            self.ffmpeg_process.terminate()
            self.ffmpeg_process.wait()
        self.delete_stream_files()

    def delete_stream_files(self):
        """删除生成的流文件"""
        try:
            for file in glob.glob('stream*'):
                os.remove(file)
            print("已删除生成的流文件")
        except Exception as e:
            print(f"删除流文件失败: {str(e)}")


class DLNADeviceDiscoveryThread(QThread):
    """用于在后台发现DLNA设备的线程"""
    devices_found = pyqtSignal(list)

    def run(self):
        upnp = upnpy.UPnP()
        devices = upnp.discover()
        valid_devices = [
            device for device in devices if 'AVTransport' in device.services]
        self.devices_found.emit(valid_devices)


class ScreenDLAN(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(Qt.Tool | self.windowFlags())
        global streamer
        streamer = ScreenStreamer()  # 初始化流媒体服务
        self.selected_device = None
        self.initUI()
   
    def initUI(self):
        self.setWindowTitle('DLNA投屏控制')
        self.setGeometry(800, 600, 200, 150)

        layout = QVBoxLayout()

        self.device_label = QLabel('选择设备:')
        layout.addWidget(self.device_label)

        self.device_combo = QComboBox()
        layout.addWidget(self.device_combo)

        self.cast_button = QPushButton('投屏')
        self.cast_button.clicked.connect(self.start_casting)
        layout.addWidget(self.cast_button)

        self.stop_button = QPushButton('停止')
        self.stop_button.clicked.connect(self.stop_casting)
        layout.addWidget(self.stop_button)

        self.setLayout(layout)

        # 启动设备发现线程
        self.discovery_thread = DLNADeviceDiscoveryThread()
        self.discovery_thread.devices_found.connect(self.populate_device_list)
        self.discovery_thread.start()

        # 启动流媒体服务
        # streamer.start()

    def populate_device_list(self, devices):
        """填充设备列表"""
        self.devices = devices
        for device in devices:
            self.device_combo.addItem(device.friendly_name)

    def get_device_info(self, dev):
        """获取设备信息"""
        try:
            base_url = dev.base_url
            control_url = dev.services["AVTransport"].control_url
            if not control_url.startswith('http://') and not control_url.startswith('https://'):
                control_url = f"{base_url}{control_url}"
            return {
                "name": dev.friendly_name,
                "control_url": control_url
            }
        except Exception as e:
            print(f"获取设备信息失败: {str(e)}")
            return None

    def build_soap_envelope(self, media_url):
        """构建SOAP请求"""
        metadata = f"""
        <DIDL-Lite xmlns:dc="http://purl.org/dc/elements/1.1/"
                   xmlns:upnp="urn:schemas-upnp-org:metadata-1-0/upnp/"
                   xmlns="urn:schemas-upnp-org:metadata-1-0/DIDL-Lite/">
            <item id="1" parentID="0" restricted="0">
                <dc:title>Live Screen</dc:title>
                <upnp:class>object.item.videoItem</upnp:class>
                <res protocolInfo="http-get:*:video/mp2t:DLNA.ORG_OP=01;DLNA.ORG_CI=0;DLNA.ORG_FLAGS=01500000000000000000000000000000"
                     resolution="1920x1080">{escape(media_url)}</res>
            </item>
        </DIDL-Lite>
        """

        return f"""
        <?xml version="1.0"?>
        <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"
                    s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
            <s:Body>
                <u:SetAVTransportURI xmlns:u="urn:schemas-upnp-org:service:AVTransport:1">
                    <InstanceID>0</InstanceID>
                    <CurrentURI>{escape(media_url)}</CurrentURI>
                    <CurrentURIMetaData>{escape(metadata)}</CurrentURIMetaData>
                </u:SetAVTransportURI>
            </s:Body>
        </s:Envelope>
        """

    def cast_to_device(self, device_info, media_url):
        """执行投屏操作"""
        try:
            headers = {
                'Content-Type': 'text/xml; charset="utf-8"',
                'SOAPAction': '"urn:schemas-upnp-org:service:AVTransport:1#SetAVTransportURI"'
            }
            body = self.build_soap_envelope(media_url)
            response = requests.post(
                device_info['control_url'],
                data=body,
                headers=headers,
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            print(f"投屏请求异常: {str(e)}")
            return False

    def play_control(self, device_info, command='Play'):
        """播放控制命令"""
        actions = {
            'Play': {'action': 'Play', 'params': '<Speed>1</Speed>'},
            'Stop': {'action': 'Stop', 'params': ''}
        }
        try:
            headers = {
                'Content-Type': 'text/xml; charset="utf-8"',
                'SOAPAction': f'"urn:schemas-upnp-org:service:AVTransport:1#{command}"'
            }
            body = f"""
            <?xml version="1.0"?>
            <s:Envelope xmlns:s="http://schemas.xmlsoap.org/soap/envelope/"
                        s:encodingStyle="http://schemas.xmlsoap.org/soap/encoding/">
                <s:Body>
                    <u:{command} xmlns:u="urn:schemas-upnp-org:service:AVTransport:1">
                        <InstanceID>0</InstanceID>
                        {actions[command]['params']}
                    </u:{command}>
            </s:Body>
            </s:Envelope>
            """
            response = requests.post(
                device_info['control_url'],
                data=body,
                headers=headers,
                timeout=3
            )
            return response.status_code == 200
        except Exception as e:
            print(f"控制命令{command}失败: {str(e)}")
            return False

    def get_local_ip(self):
        """获取本机有效IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip=s.getsockname()[0]
            s.close()
            return ip
        except:
            return socket.gethostbyname(socket.gethostname())

    def start_casting(self):
        """开始投屏"""
        selected_index = self.device_combo.currentIndex()
        if selected_index < 0:
            print("请先选择一个设备")
            return

        self.selected_device = self.devices[selected_index]
        device_info = self.get_device_info(self.selected_device)
        if not device_info:
            print("无法获取设备信息")
            return

        local_ip = self.get_local_ip()
        media_url = f"http://{local_ip}:{streamer.port}/stream.m3u8"

        if self.cast_to_device(device_info, media_url):
            self.play_control(device_info)
            print("投屏成功！")
        else:
            print("投屏失败")
         # 启动流媒体服务
        streamer.start()

    def stop_casting(self):
        """停止投屏"""
        if self.selected_device:
            device_info = self.get_device_info(self.selected_device)
            if device_info:
                self.play_control(device_info, 'Stop')
                print("投屏已停止")
        """停止投屏"""  
        streamer.stop()  
if __name__ == '__main__':
    app=QApplication(sys.argv)
    window = ScreenDLAN()
    window.show()
    sys.exit(app.exec_())
