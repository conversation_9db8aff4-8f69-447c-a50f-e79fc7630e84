import sys
import json
import requests
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QMessageBox, QApplication,
                             QFrame, QCheckBox)
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt, pyqtSignal


class LoginWindow(QDialog):
    # 定义登录成功信号，传递教师信息
    login_success = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        # 加载配置文件
        self.config = self.load_config()
        # 初始化会话对象和教师信息变量
        self.session = None
        self.teacher_info = None
        # 初始化用户界面
        self.init_ui()
        # 加载保存的登录信息
        self.load_saved_login_info()  
        
    def load_config(self):
        # 加载配置文件（服务器地址和存储的教师信息）
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")

    def init_ui(self):
        # 初始化用户界面
        # 设置窗口标题、大小和位置
        self.setWindowTitle('小马助教系统')
        self.setFixedSize(450, 400)  # 增加窗口大小
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        # 设置窗口居中显示
        self.center_window()

        # 创建主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(30, 20, 30, 20)
        
        # 添加标题标签
        title_label = QLabel('教师登录')
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont('Arial', 18, QFont.Bold))
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建登录表单框架
        form_frame = QFrame()
        form_layout = QVBoxLayout(form_frame)
        form_layout.setSpacing(15)
        form_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建用户ID输入区域
        user_id_layout = QHBoxLayout()
        user_id_label = QLabel('教师ID:')
        user_id_label.setFixedWidth(50)
        user_id_label.setFont(QFont('Arial', 10))
        self.user_id_input = QLineEdit()
        self.user_id_input.setPlaceholderText('教师ID')
        self.user_id_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        user_id_layout.addWidget(user_id_label)
        user_id_layout.addWidget(self.user_id_input)
        form_layout.addLayout(user_id_layout)

        # 创建密码输入区域
        password_layout = QHBoxLayout()
        password_label = QLabel('密码:')
        password_label.setFixedWidth(50)
        password_label.setFont(QFont('Arial', 10))
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('请输入密码')
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        form_layout.addLayout(password_layout)
        
        # 创建记住密码复选框
        self.remember_checkbox = QCheckBox('记住登录信息')
        self.remember_checkbox.setChecked(False)  # 默认不选中
        self.remember_checkbox.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        form_layout.addWidget(self.remember_checkbox)
        
        main_layout.addWidget(form_frame)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        
        # 创建登录按钮
        self.login_button = QPushButton('登录')
        self.login_button.setFixedHeight(40)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.login_button.clicked.connect(self.perform_login)
        
        # 创建取消按钮
        self.cancel_button = QPushButton('取消')
        self.cancel_button.setFixedHeight(40)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.login_button)
        main_layout.addLayout(button_layout)
        
        self.setLayout(main_layout)
        
        # 绑定回车键登录
        self.password_input.returnPressed.connect(self.perform_login)
        self.user_id_input.returnPressed.connect(self.perform_login)
    
    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        window_geometry = self.frameGeometry()
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())

    def load_saved_login_info(self):
        """加载保存的登录信息"""
        try:
            # 从配置文件中读取保存的登录信息
            saved_user_id = self.config.get('saved_login', {}).get('user_id', '')
            saved_password = self.config.get('saved_login', {}).get('password', '')
            remember_login = self.config.get('saved_login', {}).get('remember', False)

            if remember_login and saved_user_id:
                self.user_id_input.setText(saved_user_id)
                if saved_password:
                    self.password_input.setText(saved_password)
                self.remember_checkbox.setChecked(True)
        except Exception as e:
            print(f"加载保存的登录信息失败: {e}")
    
    def perform_login(self):
        """执行登录操作"""
        user_id = self.user_id_input.text().strip()
        password = self.password_input.text().strip()
        
        if not user_id:
            QMessageBox.warning(self, '输入错误', '请输入教师ID')
            self.user_id_input.setFocus()
            return
            
        if not password:
            QMessageBox.warning(self, '输入错误', '请输入密码')
            self.password_input.setFocus()
            return
        
        # 禁用登录按钮，防止重复点击
        self.login_button.setEnabled(False)
        self.login_button.setText('登录中...')
        
        try:
            # 创建会话
            self.session = requests.Session()
            
            # 先获取登录页面
            response = self.session.get(
                f"{self.config['server']['url']}/login",
                timeout=self.config['server']['timeout']
            )
            
            if response.status_code != 200:
                raise Exception(f"无法访问登录页面: {response.status_code}")
            
            # 提交登录表单
            login_data = {
                'user_id': user_id,
                'password': password
            }
            
            response = self.session.post(
                f"{self.config['server']['url']}/login",
                data=login_data,
                timeout=self.config['server']['timeout']
            )
            
            # 检查登录结果
            if response.status_code == 200:
                # 检查是否重定向到教师页面
                if '/dashboard' in response.url or 'teacher' in response.text.lower():
                    # 登录成功，获取教师详细信息
                    try:
                        teacher_info_response = self.session.get(
                            f"{self.config['server']['url']}/teacher/get_teacher_info",
                            timeout=self.config['server']['timeout']
                        )
                        teacher_name = '教师'  # 默认值
                        if teacher_info_response.status_code == 200:
                            teacher_data = teacher_info_response.json()
                            if teacher_data.get('status') == 'success':
                                teacher_info_data = teacher_data.get('teacher_info', {})
                                teacher_name = teacher_info_data.get('name', '教师')
                    except Exception as e:
                        print(f"获取教师信息失败: {e}")
                        teacher_name = '教师'

                    # 设置教师信息
                    self.teacher_info = {
                        'user_id': user_id,
                        'teacher_name': teacher_name,
                        'session': self.session,
                        'server_url': self.config['server']['url']
                    }

                    # 保存或清除登录信息
                    if self.remember_checkbox.isChecked():
                        self.save_login_info(user_id, password)
                    else:
                        self.clear_saved_login_info()

                    # 发送登录成功信号
                    self.login_success.emit(self.teacher_info)
                    self.accept()
                    return
                else:
                    raise Exception("用户ID或密码错误")
            else:
                raise Exception(f"登录失败: HTTP {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, '网络错误', f'无法连接到服务器:\n{str(e)}\n\n请检查网络连接和服务器状态')
        except Exception as e:
            QMessageBox.warning(self, '登录失败', str(e))
        finally:
            # 恢复登录按钮
            self.login_button.setEnabled(True)
            self.login_button.setText('登录')
    
    def save_login_info(self, user_id, password):
        """保存登录信息到配置文件"""
        try:
            # 创建saved_login节点来保存用户的登录信息
            if 'saved_login' not in self.config:
                self.config['saved_login'] = {}

            self.config['saved_login']['user_id'] = user_id
            self.config['saved_login']['password'] = password
            self.config['saved_login']['remember'] = True

            with open('config.json', 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存登录信息失败: {e}")

    def clear_saved_login_info(self):
        """清除保存的登录信息"""
        try:
            if 'saved_login' in self.config:
                self.config['saved_login'] = {
                    'user_id': '',
                    'password': '',
                    'remember': False
                }
                with open('config.json', 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"清除登录信息失败: {e}")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    login_window = LoginWindow()
    
    def on_login_success(teacher_info):
        print(f"登录成功: {teacher_info}")
        app.quit()
    
    login_window.login_success.connect(on_login_success)
    
    if login_window.exec_() == QDialog.Accepted:
        print("登录窗口关闭")
    
    sys.exit(app.exec_())
